<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试关于标签</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #6366f1;
            color: #6366f1;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .readme-content {
            max-height: 70vh;
            overflow-y: auto;
            padding: 16px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>思源媒体播放器设置</h1>
        
        <div class="tabs">
            <button class="tab" data-tab="account">账号</button>
            <button class="tab" data-tab="player">播放器</button>
            <button class="tab" data-tab="general">通用</button>
            <button class="tab active" data-tab="about">关于</button>
        </div>

        <div class="tab-content" id="account">
            <h3>账号设置</h3>
            <p>这里是账号配置内容...</p>
        </div>

        <div class="tab-content" id="player">
            <h3>播放器设置</h3>
            <p>这里是播放器配置内容...</p>
        </div>

        <div class="tab-content" id="general">
            <h3>通用设置</h3>
            <p>这里是通用配置内容...</p>
        </div>

        <div class="tab-content active" id="about">
            <div id="readme-content">
                <p>正在加载README内容...</p>
            </div>
        </div>
    </div>

    <script>
        // 标签切换功能
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有活动状态
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加当前活动状态
                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 加载README内容
        async function loadReadme() {
            try {
                const response = await fetch('./README_zh_CN.md');
                const content = await response.text();
                document.getElementById('readme-content').innerHTML = content;
            } catch (error) {
                document.getElementById('readme-content').innerHTML = '<p style="color: red;">无法加载README内容: ' + error.message + '</p>';
            }
        }

        // 页面加载时执行
        loadReadme();
    </script>
</body>
</html>
