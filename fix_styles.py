#!/usr/bin/env python3
import re

def fix_readme_styles():
    with open('README_zh_CN.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换白色背景
    content = re.sub(
        r'background: white;',
        'background: var(--b3-theme-surface);',
        content
    )
    
    # 替换固定颜色的边框
    content = re.sub(
        r'border-left: 4px solid #0369a1;',
        'border-left: 4px solid var(--b3-theme-primary);',
        content
    )
    
    # 替换固定颜色的文字
    content = re.sub(
        r'color: #0369a1;',
        'color: var(--b3-theme-primary);',
        content
    )
    
    # 替换固定的边框颜色
    content = re.sub(
        r'border-top: 1px solid #f3e8ff;',
        'border-top: 1px solid var(--b3-theme-border);',
        content
    )
    
    content = re.sub(
        r'border-top: 1px solid #e0f2fe;',
        'border-top: 1px solid var(--b3-theme-border);',
        content
    )
    
    # 替换阴影
    content = re.sub(
        r'box-shadow: 0 2px 4px rgba\([^)]+\);',
        'box-shadow: 0 2px 4px var(--b3-theme-shadow-light);',
        content
    )

    # 替换功能列表中的彩色边框为统一的主题色
    color_borders = [
        '#6366f1', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b',
        '#ef4444', '#ea580c', '#f97316'
    ]

    for color in color_borders:
        content = re.sub(
            f'border-left: 4px solid {color};',
            'border-left: 4px solid var(--b3-theme-primary);',
            content
        )

    # 修复遗留的警告框样式
    content = re.sub(
        r'<div style="padding: 0\.8em; background: #fef2f2; border: 1px solid #fca5a5;',
        '<div style="padding: 0.8em; background: var(--b3-theme-error-lighter); border: 1px solid var(--b3-theme-error-light);',
        content
    )

    content = re.sub(
        r'<strong style="color: #dc2626;">',
        '<strong style="color: var(--b3-theme-error);">',
        content
    )

    with open('README_zh_CN.md', 'w', encoding='utf-8') as f:
        f.write(content)

    print("样式修复完成！")

if __name__ == '__main__':
    fix_readme_styles()
