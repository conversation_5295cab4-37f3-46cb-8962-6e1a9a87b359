<script lang="ts">
    import { onMount } from "svelte";
    import { showMessage, getFrontend, Menu } from "siyuan";
    import type { ISettingItem, SettingType } from "../core/types";
    import Tabs from './Tabs.svelte';
    import { notebook } from "../core/document";
    import { QRCodeManager, isBilibiliAvailable } from "../core/bilibili";
    import { LicenseManager, type LicenseInfo } from "../core/license";
    import { WebDAVManager } from "../core/webdav";
    import { OpenListManager } from "../core/openlist";

    export let group: string;
    export let config: any;
    export let i18n: any;
    export let activeTabId = 'settings';
    export let plugin: any;

    // 工具函数
    const msg = { success: (m) => showMessage(m, 2000, 'info'), error: (m) => showMessage(m, 3000, 'error') };
    const getConfig = async () => await plugin.loadData('config.json') || {};
    const saveAppState = async () => { const cfg = await getConfig(); cfg.settings = appState.config; await plugin.saveData('config.json', cfg, 2); window.dispatchEvent(new CustomEvent('configUpdated', { detail: cfg })); };
    const processDbId = async (id: string) => { if (!id || !/^\d{14}-[a-z0-9]{7}$/.test(id)) return { id, avId: '' }; const avId = (await fetch('/api/query/sql', { method: 'POST', body: JSON.stringify({ stmt: `SELECT markdown FROM blocks WHERE type='av' AND id='${id}'` }) }).then(r => r.json()).catch(() => ({ data: [] }))).data?.[0]?.markdown?.match(/data-av-id="([^"]+)"/)?.[1]; return { id, avId: avId || id }; };
    const initDb = async (id: string) => { try { const { avId } = await processDbId(id); return !!(await fetch('/api/av/getAttributeView', { method: 'POST', body: JSON.stringify({ id: avId }) }).then(r => r.json()).catch(() => ({ code: -1 }))).code === 0; } catch { return false; } };

    // 统一状态管理
    let appState = {
        config: {
            openMode: "default", playerType: "built-in", playerPath: "PotPlayerMini64.exe",
            volume: 70, speed: 100, showSubtitles: false, enableDanmaku: false,
            loopCount: 3, pauseAfterLoop: false, loopPlaylist: false, loopSingle: false,
            insertMode: "updateBlock", enableDatabase: false, screenshotWithTimestamp: false,
            notebook: { id: '', name: '' }, parentDoc: { id: '', name: '', path: '' }, playlistDb: { id: '', avId: '' },
            playlistView: { mode: 'detailed', tab: '目录', expanded: [] },
            linkFormat: "- [😄标题 艺术家 字幕 时间](链接)",
            mediaNotesTemplate: "# 📽️ 标题的媒体笔记\n- 📅 日 期：日期\n- ⏱️ 时 长：时长\n- 🎨 艺 术 家：艺术家\n- 🔖 类 型：类型\n-  链 接：[链接](链接)\n- ![封面](封面)\n- 📝 笔记内容：",
            webdavAccounts: [], openlistAccounts: [], bilibiliAccounts: []
        },
        ui: { activeTab: 'account', editingAccount: null, editingData: {}, licenseCode: '' },
        runtime: { notebooks: [], notebookOptions: [], currentLicense: null, qrCodeManager: null, qrcode: { data: '', key: '' } }
    };

    // 响应式计算
    $: accounts = [...(appState.config.webdavAccounts || []).map(acc => ({ ...acc, type: 'webdav' })), ...(appState.config.openlistAccounts || []).map(acc => ({ ...acc, type: 'openlist' })), ...(appState.config.bilibiliAccounts || []).map(acc => ({ ...acc, type: 'bilibili' }))];
    $: settingItems = createSettings(appState.config, accounts);

    // 配置定义
    const ACCOUNT_TYPES = {
        webdav: { name: 'WebDAV', fields: ['server', 'username', 'password'] },
        openlist: { name: 'OpenList', fields: ['server', 'username', 'password'] },
        ...(isBilibiliAvailable() ? { bilibili: { name: 'B站', fields: [] } } : {})
    };

    const SETTING_CONFIGS = {
        player: [
            { key: "openMode", type: "select", title: () => i18n.setting?.items?.openMode?.title || "打开方式",
              description: () => i18n.setting?.items?.openMode?.description || "选择播放器打开模式",
              displayCondition: () => !getFrontend().endsWith('mobile'),
              options: [
                { label: () => i18n.setting?.items?.openMode?.options?.default || "新标签", value: "default" },
                { label: () => i18n.setting?.items?.openMode?.options?.right || "右侧新标签", value: "right" },
                { label: () => i18n.setting?.items?.openMode?.options?.bottom || "底部新标签", value: "bottom" },
                { label: () => i18n.setting?.items?.openMode?.options?.window || "新窗口", value: "window" }
              ] },
            { key: "playerType", type: "select", title: () => i18n.setting?.items?.playerType?.title || "播放器选择",
              description: () => i18n.setting?.items?.playerType?.description || "选择用于播放媒体的播放器",
              options: [
                { label: () => i18n.setting?.items?.playerType?.builtIn || "内置播放器", value: "built-in" },
                { label: () => i18n.setting?.items?.playerType?.potPlayer || "PotPlayer", value: "potplayer" },
                { label: () => i18n.setting?.items?.playerType?.browser || "浏览器", value: "browser" }
              ] },
            { key: "playerPath", type: "textarea", rows: 1, title: () => i18n.setting?.items?.playerPath?.title || "播放器路径",
              description: () => i18n.setting?.items?.playerPath?.description || "外部播放器的可执行文件路径",
              displayCondition: () => settingItems.find(i => i.key === 'playerType')?.value === 'potplayer' },
            { key: "volume", type: "slider", title: () => i18n.setting?.items?.volume?.title || "音量",
              description: () => i18n.setting?.items?.volume?.description || "默认音量大小",
              slider: { min: 0, max: 100, step: 1 } },
            { key: "speed", type: "slider", title: () => i18n.setting?.items?.speed?.title || "播放速度",
              description: () => i18n.setting?.items?.speed?.description || "默认播放速度",
              slider: { min: 50, max: 500, step: 50 } },
            { key: "showSubtitles", type: "checkbox", title: () => i18n.setting?.items?.showSubtitles?.title || "显示字幕",
              description: () => i18n.setting?.items?.showSubtitles?.description || "播放时显示字幕（如果有）" },
            { key: "enableDanmaku", type: "checkbox", title: () => i18n.setting?.items?.enableDanmaku?.title || "启用弹幕",
              description: () => i18n.setting?.items?.enableDanmaku?.description || "播放时启用弹幕（如果有）" },
            { key: "loopCount", type: "slider", title: () => i18n.setting?.items?.loopCount?.title || "片段循环次数",
              description: () => i18n.setting?.items?.loopCount?.description || "片段循环播放次数",
              slider: { min: 1, max: 10, step: 1 } },
            { key: "pauseAfterLoop", type: "checkbox", title: () => i18n.setting?.items?.pauseAfterLoop?.title || "片段循环后暂停",
              description: () => i18n.setting?.items?.pauseAfterLoop?.description || "片段循环结束后暂停播放媒体" },
            { key: "loopPlaylist", type: "checkbox", title: () => i18n.setting?.items?.loopPlaylist?.title || "列表循环",
              description: () => i18n.setting?.items?.loopPlaylist?.description || "自动播放下一个媒体",
              onChange: (v) => (appState.config.loopPlaylist = v, v && (appState.config.loopSingle = false)) },
            { key: "loopSingle", type: "checkbox", title: () => i18n.setting?.items?.loopSingle?.title || "单项循环",
              description: () => i18n.setting?.items?.loopSingle?.description || "循环播放当前媒体",
              onChange: (v) => (appState.config.loopSingle = v, v && (appState.config.loopPlaylist = false)) }
        ],
        general: [
            { key: "enableDatabase", type: "checkbox", title: () => i18n.setting?.items?.enableDatabase?.title || "绑定数据库",
              description: () => i18n.setting?.items?.enableDatabase?.description || "启用播放列表数据库功能，用于保存和管理媒体项目" },
            { key: "playlistDb", type: "textarea", rows: 1, title: () => i18n.setting?.items?.playlistDb?.title || "播放列表数据库",
              displayCondition: () => appState.config.enableDatabase,
              description: () => appState.config.playlistDb?.avId
                ? (i18n.setting?.items?.playlistDb?.avIdDescription?.replace('${avId}', appState.config.playlistDb.avId) || `属性视图ID: ${appState.config.playlistDb.avId}`)
                : (i18n.setting?.items?.playlistDb?.description || "输入数据库ID（支持数据库块ID或数据库ID/avid，格式：14位数字-7位字符）"),
              onChange: async (v) => {
                appState.config.playlistDb = v ? await processDbId(v) : { id: '', avId: '' };
                if (appState.config.playlistDb.avId) await initDb(v).catch(() => {});
              } },
            { key: "targetDocumentSearch", type: "text", title: () => i18n.setting?.items?.mediaNoteLocation?.search?.title || "媒体笔记创建位置",
              description: () => i18n.setting?.items?.mediaNoteLocation?.search?.description || "输入关键字后按回车搜索文档",
              onKeydown: async (e) => {
                if (e.key === 'Enter') {
                  const r = await notebook.searchAndUpdate(e.target.value, appState.config, { getConfig, saveConfig: async (cfg) => { await plugin.saveData('config.json', cfg, 2); window.dispatchEvent(new CustomEvent('configUpdated', { detail: cfg })); } });
                  if (r.success && r.docs) appState.runtime.notebookOptions = [...appState.runtime.notebooks.map(nb => ({ label: nb.name, value: nb.id })), ...r.docs.map(doc => ({ label: doc.hPath || '无标题', value: doc.path?.split('/').pop()?.replace('.sy', '') || doc.id, notebook: doc.box, path: doc.path?.replace('.sy', '') || '' }))];
                }
              } },
            { key: "targetNotebook", type: "select", title: () => i18n.setting?.items?.mediaNoteLocation?.target?.title || "媒体笔记目标笔记本/文档",
              description: () => appState.config.parentDoc?.id
                ? `目标文档：${appState.config.parentDoc.name}`
                : (appState.config.notebook?.id ? `目标笔记本：${appState.config.notebook.name}` : (i18n.setting?.items?.mediaNoteLocation?.target?.description || "选择创建媒体笔记的目标笔记本")),
              onChange: (v) => {
                const nb = appState.runtime.notebooks.find(nb => nb.id === v);
                const doc = appState.runtime.notebookOptions.find(opt => opt.value === v);
                nb ? (appState.config.notebook = { id: v, name: nb.name }, appState.config.parentDoc = { id: '', name: '', path: '' }) : doc && (appState.config.parentDoc = { id: v, name: doc.label, path: doc.path || '' }, appState.config.notebook = { id: doc.notebook || '', name: '' });
              },
              options: () => appState.runtime.notebookOptions.length ? appState.runtime.notebookOptions : appState.runtime.notebooks.map(nb => ({ label: nb.name, value: nb.id })) },
            { key: "insertMode", type: "select", title: () => i18n.setting?.items?.insertMode?.title || "插入方式",
              description: () => i18n.setting?.items?.insertMode?.description || "选择链接和笔记的插入方式",
              onChange: (v) => appState.config.insertMode = v,
              options: [
                { label: () => i18n.setting?.items?.insertMode?.insertBlock || "插入光标处", value: "insertBlock" },
                { label: () => i18n.setting?.items?.insertMode?.appendBlock || "追加到块末尾", value: "appendBlock" },
                { label: () => i18n.setting?.items?.insertMode?.prependBlock || "添加到块开头", value: "prependBlock" },
                { label: () => i18n.setting?.items?.insertMode?.updateBlock || "更新当前块", value: "updateBlock" },
                { label: () => i18n.setting?.items?.insertMode?.prependDoc || "插入到文档顶部", value: "prependDoc" },
                { label: () => i18n.setting?.items?.insertMode?.appendDoc || "插入到文档底部", value: "appendDoc" },
                { label: () => i18n.setting?.items?.insertMode?.clipboard || "复制到剪贴板", value: "clipboard" }
              ] },
            { key: "screenshotWithTimestamp", type: "checkbox", title: () => i18n.setting?.items?.screenshotWithTimestamp?.title || "截图包含时间戳",
              description: () => i18n.setting?.items?.screenshotWithTimestamp?.description || "启用后，截图功能也会添加时间戳链接" },
            { key: "linkFormat", type: "textarea", rows: 1, title: () => i18n.setting?.items?.linkFormat?.title || "链接格式",
              description: () => i18n.setting?.items?.linkFormat?.description || "设置链接显示格式，可使用 时间 字幕 标题 艺术家 链接 截图，支持同时插入链接和截图" },
            { key: "mediaNotesTemplate", type: "textarea", rows: 9, title: () => i18n.setting?.items?.mediaNotesTemplate?.title || "媒体笔记模板",
              description: () => i18n.setting?.items?.mediaNotesTemplate?.description || "设置媒体笔记的模板格式，支持使用 时间 标题 艺术家 链接 时长 封面 类型 ID 日期 时间戳 等变量" }
        ]
    };

    // 统一操作管理
    const updateAccount = (type, account, operation) => {
        const key = type + 'Accounts';
        appState.config[key] = appState.config[key] || [];
        if (operation === 'add') appState.config[key].push({ ...account, id: Date.now().toString() });
        else if (operation === 'edit') {
            const i = appState.config[key].findIndex(a => a.id === appState.ui.editingAccount);
            appState.config[key][i] = { ...appState.config[key][i], ...account };
        } else if (operation === 'delete') appState.config[key] = appState.config[key].filter(a => a.id !== account);
    };

    const accountManager = {
        save: async (type, data, isEdit) => {
            // 极简连接验证
            const managers = { webdav: WebDAVManager, openlist: OpenListManager };
            const manager = managers[type];
            if (manager) {
                const result = await manager.checkConnection(data);
                if (!result.connected) return msg.error(result.message);
            } else if (!data.server || !data.username) {
                return msg.error('服务器地址和用户名不能为空');
            }

            updateAccount(type, data, isEdit ? 'edit' : 'add');
            await saveAppState();
            msg.success(`${ACCOUNT_TYPES[type]?.name}保存成功`);
            appState.ui.editingAccount = null;
            appState.ui.editingData = {};
        },
        delete: async (type, id) => {
            type === 'pro' ? appState.runtime.currentLicense = null : (updateAccount(type, id, 'delete'), await saveAppState());
            msg.success(type === 'pro' ? 'Pro功能已关闭' : '账号已删除');
        },
        edit: (type, data) => {
            appState.ui.editingAccount = type === 'pro' ? 'pro' : data.id;
            if (type !== 'pro' && type !== 'bilibili') appState.ui.editingData = { ...data };
        },
        loginBilibili: () => {
            appState.ui.editingAccount = 'bilibili_' + Date.now();
            appState.runtime.qrCodeManager ||= new QRCodeManager(q => appState.runtime.qrcode = q, async d => {
                updateAccount('bilibili', { ...d, id: 'bili_' + d.mid }, appState.config.bilibiliAccounts?.find(a => a.mid === d.mid) ? 'edit' : 'add');
                await saveAppState(); appState.ui.editingAccount = null; appState.runtime.qrCodeManager?.stopPolling(); msg.success('B站登录成功');
            });
            appState.runtime.qrCodeManager.startLogin();
        }};

    const activatePro = async (code = appState.ui.licenseCode) => {
        const r = await LicenseManager.activate(String(code || ''), plugin);
        appState.runtime.currentLicense = r.license || null;
        if (r.success && appState.ui.licenseCode) appState.ui.licenseCode = '';
        showMessage(r.message || r.error, r.success ? 2000 : (r.isHtml ? 0 : 3000), r.success ? 'info' : 'error');
        await saveAppState();
        appState.ui.editingAccount = null;
    };

    let readmeContent = '', toc = [];
    fetch('/plugins/siyuan-media-player/README_zh_CN.md').then(r => r.text()).then(t => {
        toc = [...t.matchAll(/<h2[^>]*>([^<]+)</g)].map((m, i) => ({ title: m[1], id: `toc-${i}` }));
        readmeContent = t.replace(/<h2([^>]*)>([^<]+)</g, (m, attrs, title) => `<h2${attrs} id="toc-${toc.findIndex(t => t.title === title)}">${title}</h2>`);
    }).catch(() => readmeContent = '无法加载README内容');

    // 工具函数
    const tabs = [
        { id: 'account', name: i18n.setting?.tabs?.account || '账号' },
        { id: 'player', name: i18n.setting?.tabs?.player || '播放器' },
        { id: 'general', name: i18n.setting?.tabs?.general || '通用' },
        { id: 'about', name: i18n.setting?.tabs?.about || '关于' } ];

    const renderDesc = (d) => d?.icon
        ? `${d.icon.startsWith('#') ? `<svg class="acc-icon"><use xlink:href="${d.icon}"></use></svg>` : `<img src="${d.icon}" class="acc-icon">`}<div class="acc-info"><b>${d.name}</b> <span style="color:${d.statusColor}">${d.status}</span><br><small>${d.info1}</small><br><small class="acc-muted">${d.info2}</small></div>`
        : d;

    const getTypeInfo = (type) => ({ trial: { name: '体验会员', color: '#ff9800' }, annual: { name: '年付会员', color: '#4caf50' }, dragon: { name: '恶龙会员', color: '#9c27b0' } }[type] || { name: type, color: '#666' });

    const createAccountItems = (accounts) => [
        { key: "proLicense", type: "account", tab: "account",
          description: appState.runtime.currentLicense?.isValid
            ? { icon: appState.runtime.currentLicense.type === 'dragon' ? '#iconDragon' : '#iconVIP',
                name: appState.runtime.currentLicense.userName,
                status: getTypeInfo(appState.runtime.currentLicense.type).name,
                statusColor: getTypeInfo(appState.runtime.currentLicense.type).color,
                info1: `用户ID: ${appState.runtime.currentLicense.userId}`,
                info2: `期限: ${appState.runtime.currentLicense.expiresAt === 0 ? '永久有效' : new Date(appState.runtime.currentLicense.expiresAt).toLocaleDateString()}` }
            : { icon: '#iconVIP', name: '申请Pro会员', status: '', statusColor: '', info1: '享受完整功能体验', info2: '激活码或申请体验' },
          accountData: { type: 'pro', id: 'pro', isValid: !!appState.runtime.currentLicense?.isValid } },
        ...accounts.sort((a, b) => ({ webdav: 1, openlist: 2, bilibili: 3 }[a.type] || 999) - ({ webdav: 1, openlist: 2, bilibili: 3 }[b.type] || 999))
          .map(acc => ({
            key: `account_${acc.id}`, type: "account", tab: "account",
            description: {
              icon: acc.type === 'bilibili' ? acc.face || '#iconBili' : acc.type === 'webdav' ? '/plugins/siyuan-media-player/assets/images/webdav.svg' : '/plugins/siyuan-media-player/assets/images/openlist.svg',
              name: acc.uname || acc.username || ACCOUNT_TYPES[acc.type]?.name,
              status: acc.type === 'bilibili' ? `LV${acc.level_info?.current_level}` : '已连接',
              statusColor: '#4caf50',
              info1: acc.type === 'bilibili' ? `UID ${acc.mid}` : acc.server,
              info2: acc.type === 'bilibili' ? `硬币 ${acc.money}` : `用户: ${acc.username}`
            },
            accountData: acc
          }))
    ];

    const createSettings = (config, accounts) => [
        ...createAccountItems(accounts || []),
        ...Object.entries(SETTING_CONFIGS).flatMap(([tab, configs]) =>
          configs.map(cfg => ({
            ...cfg, tab,
            value: config[cfg.key] || (cfg.key === 'linkFormat' ? "- [😄标题 艺术家 字幕 时间](链接)" : cfg.key === 'mediaNotesTemplate' ? "# 📽️ 标题的媒体笔记\n- 📅 日 期：日期\n- ⏱️ 时 长：时长\n- 🎨 艺 术 家：艺术家\n- 🔖 类 型：类型\n- 🔗 链 接：[链接](链接)\n- ![封面](封面)\n- 📝 笔记内容：" : ""),
            title: typeof cfg.title === 'function' ? cfg.title() : cfg.title,
            description: typeof cfg.description === 'function' ? cfg.description() : cfg.description,
            options: cfg.options ? (typeof cfg.options === 'function' ? cfg.options() : cfg.options.map(opt => ({ ...opt, label: typeof opt.label === 'function' ? opt.label() : opt.label }))) : undefined
          }))
        )
    ];

    // 初始化和处理函数
    const refreshSettings = async () => {
        try {
            appState.config = { ...appState.config, ...((await getConfig()).settings || {}) };

            if (!appState.runtime.currentLicense) {
                appState.runtime.currentLicense = await LicenseManager.load(plugin).catch(() => null);
                if (appState.runtime.currentLicense?.type === 'trial') {
                    const expireDate = new Date(appState.runtime.currentLicense.expiresAt).toLocaleDateString();
                    showMessage(`体验会员已激活（到期：${expireDate}）🎯 升级享受完整功能`, 4000, 'info');
                }
            }

            if (!appState.runtime.notebooks.length) {
                appState.runtime.notebooks = await notebook.getList?.().catch(() => []) || [];
                appState.runtime.notebookOptions = [...appState.runtime.notebooks.map(nb => ({ label: nb.name, value: nb.id })), ...(appState.config.parentDoc?.id ? [{ label: appState.config.parentDoc.name, value: appState.config.parentDoc.id, path: appState.config.parentDoc.path }] : [])];
            }
        } catch (e) { console.warn('Settings refresh failed:', e); }
    };

    const handleChange = async (e, item) => {
        const v = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
        item.onChange ? await item.onChange(v) : appState.config[item.key] = v;
        await saveAppState();
    };

    // 生命周期
    let initialized = false;
    $: if (appState.ui.activeTab && !initialized) { refreshSettings(); initialized = true; }
    onMount(() => { refreshSettings(); initialized = true; });

    // 全局函数
    (globalThis as any).copyUserInfo = async () => {
        const user = await LicenseManager.getSiYuanUserInfo();
        if (!user) return msg.error('请先登录思源账号');
        await navigator.clipboard.writeText(JSON.stringify({userId: user.userId, userName: user.userName}));
        msg.success('用户信息已复制');
    };
</script>

<div class="panel common-panel" data-name={group}>
    <!-- 统一导航 -->
    <Tabs {activeTabId} {i18n}>
        <svelte:fragment slot="controls">
            <span class="panel-count">{tabs.find(tab => tab.id === appState.ui.activeTab)?.name || i18n.setting.description}</span>
        </svelte:fragment>
    </Tabs>

    <div class="panel-tabs">
        {#each tabs as tab}
            <button class="tab" class:active={appState.ui.activeTab === tab.id} on:click={() => appState.ui.activeTab = tab.id}>{tab.name}</button>
        {/each}
    </div>

    <div class="panel-content">
        {#each settingItems as item (item.key)}
            {#if item.tab === appState.ui.activeTab && (!item.displayCondition || item.displayCondition(appState.config))}
            <!-- 分组标题 -->
            {#if item.type === 'account' && item.accountData?.type !== 'pro'}
                {@const prev = settingItems[settingItems.indexOf(item) - 1]}
                {#if !prev?.accountData || prev.accountData.type !== item.accountData.type}
                    <div style="color: var(--b3-theme-on-surface-light); font-size: 14px; font-weight: 500;">{ACCOUNT_TYPES[item.accountData.type]?.name}</div>
                {/if}
            {/if}

            <div class="setting-item setting-item-{item.type}" data-key={item.key}>
                <div class="setting-info">
                    {#if item.type !== 'account'}<div class="setting-title">{item.title}</div>{/if}
                    {#if item.description}<div class="setting-description {item.description?.icon ? 'acc-desc' : ''}">{@html renderDesc(item.description)}</div>{/if}
                    {#if item.type === 'slider'}<div class="slider-wrapper"><input type="range" min={item.slider?.min ?? 0} max={item.slider?.max ?? 100} step={item.slider?.step ?? 1} value={appState.config[item.key]} on:input={(e) => handleChange(e, item)}><span class="slider-value">{item.key === 'speed' ? Number(appState.config[item.key]) / 100 + 'x' : appState.config[item.key]}</span></div>
                    {:else if item.type === 'text'}<input type="text" class="b3-text-field fn__block" value={String(item.value)} on:input={(e) => handleChange(e, item)} on:keydown={(e) => item.onKeydown && item.onKeydown(e)}>
                    {:else if item.type === 'textarea'}<textarea class="b3-text-field fn__block" rows={item.rows || 4} value={String(item.value)} on:input={(e) => handleChange(e, item)}></textarea>
                    {/if}
                </div>
                <div class="setting-control">
                    {#if item.type === 'checkbox'}<label class="checkbox-wrapper"><input type="checkbox" checked={Boolean(item.value)} on:change={(e) => handleChange(e, item)}><span class="checkbox-custom"></span></label>
                    {:else if item.type === 'select'}<select class="select-wrapper" style="max-width: 200px; width: 200px;" value={item.value} on:change={(e) => handleChange(e, item)}>{#each item.options || [] as option}<option value={option.value} title={option.label}>{option.label.length > 30 ? option.label.slice(0, 30) + '...' : option.label}</option>{/each}</select>
                    {:else if item.type === 'button'}<button class="b3-button b3-button--outline {item.buttonStyle || ''}" on:click={() => item.onAction && item.onAction()}>{item.buttonText || '操作'}</button>
                    {:else if item.type === 'account'}<button class="b3-button b3-button--text" on:click|preventDefault|stopPropagation={(e) => { const m = new Menu(); const type = item.accountData.type; m.addItem({ icon: 'iconEdit', label: type === 'bilibili' ? '重新登录' : type === 'pro' ? '激活' : '编辑', click: () => type === 'bilibili' ? accountManager.loginBilibili() : accountManager.edit(type, item.accountData) }); if (type === 'pro' ? item.accountData.isValid : true) m.addItem({ icon: 'iconTrashcan', label: '删除', click: () => accountManager.delete(type, item.accountData.id) }); m.open({ x: e.clientX, y: e.clientY }); }}>⋯</button>
                    {/if}
                </div>
            </div>

            <!-- 账号编辑表单 -->
            {#if item.type === 'account' && (appState.ui.editingAccount === 'pro' || accounts.find(a => a.id === appState.ui.editingAccount)) && (appState.ui.editingAccount === item.accountData.id || appState.ui.editingAccount === item.accountData.type)}
                <div class="setting-item" style="margin: 8px 0; border: 1px solid var(--b3-border-color); border-radius: 4px; padding: 16px;">
                    <div class="setting-info">
                        {#if appState.ui.editingAccount === 'pro'}<div class="setting-title">编辑Pro</div><input type="text" class="b3-text-field fn__block" bind:value={appState.ui.licenseCode} placeholder="请输入激活码" style="margin: 8px 0;"><div style="margin-top: 12px;"><button class="b3-button b3-button--text" on:click={() => activatePro()}>激活</button><button class="b3-button b3-button--cancel" on:click={() => appState.ui.editingAccount = null}>取消</button>{#if !appState.runtime.currentLicense?.isValid}<button class="b3-button b3-button--cancel" on:click={() => activatePro('')}>申请体验</button>{/if}<button class="b3-button b3-button--text" style="color: #d4af37;" on:click={() => window.open('https://pay.ldxp.cn/shop/J7MJJ8YR/zmfsuc', '_blank')}>购买</button></div>
                        {:else if appState.ui.editingAccount?.startsWith('bilibili')}<div class="setting-title">B站登录</div><img src={appState.runtime.qrcode.data} alt="登录二维码" style="width: 200px; margin: 10px 0;"><p>{appState.runtime.qrcode.message || '等待扫码'}</p><div style="margin-top: 12px;"><button class="b3-button b3-button--cancel" on:click={() => appState.ui.editingAccount = null}>取消</button></div>
                        {:else}{@const acc = accounts.find(a => a.id === appState.ui.editingAccount)}{@const type = acc?.type || appState.ui.editingAccount}<div class="setting-title">编辑{ACCOUNT_TYPES[type]?.name}</div>{#each (ACCOUNT_TYPES[type]?.fields || []) as field}<input type={field === 'password' ? 'password' : 'text'} class="b3-text-field fn__block" style="margin: 8px 0;" value={appState.ui.editingData?.[field] || ''} placeholder={field === 'server' ? '服务器地址' : field === 'username' ? '用户名' : '密码'} on:input={(e) => { appState.ui.editingData = appState.ui.editingData || {}; appState.ui.editingData[field] = e.target.value; }}>{/each}<div style="margin-top: 12px;"><button class="b3-button b3-button--text" on:click={() => accountManager.save(type, appState.ui.editingData || {}, !!acc)}>保存</button><button class="b3-button b3-button--cancel" on:click={() => { appState.ui.editingAccount = null; appState.ui.editingData = {}; }}>取消</button></div>
                        {/if}
                    </div>
                </div>
            {/if}

            {/if}
        {/each}

        <!-- 添加账号表单 -->
        {#if appState.ui.activeTab === 'account' && appState.ui.editingAccount && !accounts.find(a => a.id === appState.ui.editingAccount) && appState.ui.editingAccount !== 'pro'}
            <div class="setting-item" style="margin: 8px 0; border: 1px solid var(--b3-border-color); border-radius: 4px; padding: 16px;">
                <div class="setting-info">
                    {#if appState.ui.editingAccount?.startsWith('bilibili')}<div class="setting-title">B站登录</div><img src={appState.runtime.qrcode.data} alt="登录二维码" style="width: 200px; margin: 10px 0;"><p>{appState.runtime.qrcode.message || '等待扫码'}</p><div style="margin-top: 12px;"><button class="b3-button b3-button--cancel" on:click={() => appState.ui.editingAccount = null}>取消</button></div>
                    {:else}{@const type = appState.ui.editingAccount}<div class="setting-title">添加{ACCOUNT_TYPES[type]?.name}</div>{#each (ACCOUNT_TYPES[type]?.fields || []) as field}<input type={field === 'password' ? 'password' : 'text'} class="b3-text-field fn__block" style="margin: 8px 0;" value={appState.ui.editingData?.[field] || ''} placeholder={field === 'server' ? '服务器地址' : field === 'username' ? '用户名' : '密码'} on:input={(e) => { appState.ui.editingData = appState.ui.editingData || {}; appState.ui.editingData[field] = e.target.value; }}>{/each}<div style="margin-top: 12px;"><button class="b3-button b3-button--text" on:click={() => accountManager.save(type, appState.ui.editingData || {}, false)}>保存</button><button class="b3-button b3-button--cancel" on:click={() => { appState.ui.editingAccount = null; appState.ui.editingData = {}; }}>取消</button></div>
                    {/if}
                </div>
            </div>
        {/if}

        <!-- 添加按钮 -->
        {#if appState.ui.activeTab === 'account'}
            <div class="setting-item" style="margin-top: 20px;">
                <div class="setting-info">
                    <div class="setting-title">添加账号</div>
                </div>
                <div class="setting-control">
                    <button class="b3-button b3-button--outline" on:click|preventDefault|stopPropagation={(e) => {
                        const m = new Menu();
                        m.addItem({ icon: 'iconCloud', label: 'WebDAV', click: () => { appState.ui.editingAccount = 'webdav'; appState.ui.editingData = {}; }});
                        m.addItem({ icon: 'iconCloud', label: 'OpenList', click: () => { appState.ui.editingAccount = 'openlist'; appState.ui.editingData = {}; }});
                        if (isBilibiliAvailable()) {
                            m.addItem({ icon: 'iconBili', label: 'B站', click: () => accountManager.loginBilibili() });
                        }
                        m.open({ x: e.clientX, y: e.clientY });
                    }}>添加账号</button>
                </div>
            </div>
        {/if}

        <!-- 关于 -->
        {#if appState.ui.activeTab === 'about'}
            {#if toc.length}<div style="position: sticky; top: -10px; z-index: 10; background: var(--b3-theme-background); padding: 8px 0; border-bottom: 1px solid var(--b3-border-color); margin: -20px 0 16px 0; font-size: 12px;">{#each toc as item}<a href="#{item.id}" style="color: var(--b3-theme-primary); text-decoration: none; margin-right: 16px;" on:click|preventDefault={() => document.getElementById(item.id)?.scrollIntoView({behavior: 'smooth'})}>{item.title.replace(/[🚀⚠️🧧📖]/g, '').trim()}</a>{/each}</div>{/if}
            <div style="font-size: 12px; text-align: left;">{@html readmeContent}</div>
        {/if}
    </div>
</div>