### 📅 v0.5.0版本更新 (2025.8.5)
1.设置会员
2.增加多账号支持
3.优化暗黑模式分p列表无法看清[#101](https://github.com/mm-o/siyuan-media-player/issues/101)
4.分p列表可以显示标题 [#100](https://github.com/mm-o/siyuan-media-player/issues/100)
5.优化紧凑视图分p展开样式

### 📅 v0.4.7版本更新 (2025.7.28)
#### 🎯 **脚本系统重构** - 重新设计扩展脚本加载机制，更符合思源笔记规范
- 🔧 **简化扩展架构**: 完全重构扩展系统，直接通过window对象暴露API配置，完全符合思源笔记脚本标准
- 📜 **扩展脚本升级到v2.0.0**: 极简设计，静默加载，**必须更新到v2.0.0扩展脚本**才能正常使用第三方功能
- 📥 **v2.0.0扩展脚本地址**: [GitHub](https://github.com/mm-o/siyuan-media-player/raw/main/docs/bilibili-extension.js) | [Gitee](https://gitee.com/m-o/siyuan-media-player/blob/master/docs/bilibili-extension.js)
#### 🎯 **会员预告** 
- 🚨 **重要提示**:
  <div style="padding: 0.8em; background: #fef2f2; border: 1px solid #fca5a5; border-radius: 6px; margin: 0.5em 0;">
  <strong style="color: #dc2626;">⚠️ 下个版本预计上线会员功能，价格会上浮，已打赏用户将获得永久会员资格，请联系我加群，加群请备注打赏订单号，现在打赏也算哦</strong>
  </div>

---
### 📅 v0.4.6版本更新 (2025.7.25)
#### 🎯 **扩展架构重构** - 插件采用模块化设计，支持可选扩展功能
- 🚨 **重要提示**:
  <div style="padding: 0.8em; background: #fef2f2; border: 1px solid #fca5a5; border-radius: 6px; margin: 0.5em 0;">
  <strong style="color: #dc2626;">⚠️ 本版本已移除第三方相关API，更新后需要安装扩展脚本才能使用第三方功能</strong>
  </div>
- 💡 **安装方法**:
  1. 下载扩展脚本文件
  2. 思源笔记 → 设置 → 外观 → 代码片段 → JS → 粘贴脚本内容 → 开启
  3. 重启思源笔记
- 📥 **扩展脚本地址**: [GitHub](https://github.com/mm-o/siyuan-media-player/raw/main/docs/bilibili-extension.js) | [Gitee](https://gitee.com/m-o/siyuan-media-player/blob/master/docs/bilibili-extension.js)
#### ✨ **界面优化**
- 🎨 **按钮图标化**: 将文字按钮替换为纯图标，实现极简设计
- ⚡ **服务预初始化**: 解决首次使用时间戳链接无法播放的问题
##### 🔍 **全局搜索功能**
- 🔍 **搜索标签**: 在目录标签前添加搜索按钮，使用🔍字符图标
- 🌐 **全局搜索**: 可搜索所有标签下的所有媒体内容
- ⚡ **实时搜索**: 输入时立即显示搜索结果，支持标题、艺术家、URL搜索
- 🎯 **智能交互**: 点击显示搜索框并自动聚焦，无内容时自动隐藏

### 📅 v0.4.4版本更新 (2025.7.20)
#### 🎴 **笔记卡片可视化系统** - 全新的媒体学习内容管理体验
- 📋 **智能筛选面板**: 在笔记面板中新增媒体类型筛选功能，支持按时间戳、循环片段、截图、媒体卡片四种类型快速筛选查看
- 🖼️ **可视化卡片展示**: 筛选结果以精美卡片形式展示，自动显示封面图/截图、媒体类型、时间信息，让学习内容一目了然
- 🔗 **一键跳转播放**: 点击任意卡片即可直接跳转到对应的媒体时间点开始播放，实现从笔记到媒体的无缝衔接
- ⚡ **实时同步更新**: 在筛选状态下进行时间戳、截图等操作，新内容会立即显示在卡片列表中，保持完美的实时体验
- 🎯 **智能插入定位**: 筛选状态下的所有媒体操作会自动插入到当前文档底部，确保内容有序组织
- 🏷️ **统一属性管理**: 基于v0.4.3版本的自定义属性系统，实现所有媒体类型的统一识别和管理
#### ✨ **播放列表增强功能**
- 🌐 **外部打开支持**: 新增右键菜单"外部打开"功能
  - B站/OpenList/WebDAV等网络资源在浏览器中打开
  - 本地文件在资源管理器中定位显示
  - 智能识别资源类型，自动选择最佳打开方式
#### 📱 **移动端支持尝试**
- 🧪 **实验性功能**: 尝试添加移动端本地文件选择支持（可能在某些设备上无法正常工作）
- 🔄 **智能平台检测**: 桌面端使用原生文件对话框，移动端使用HTML5文件选择器
- ⚠️ **注意事项**: 移动端文件选择功能仍在测试阶段，建议优先使用桌面端
#### 🔧 **数据库字段优化**
- 📝 **字段描述管理**: 自动为所有播放列表字段添加描述说明，支持字段重命名后正常加载
  - **字段描述对照表**: 媒体标题、URL、时长、所在标签、来源、类型、艺术家、封面图、艺术家头像、创建时间
  - **故障排除**: 播放列表加载失败时，请检查字段描述是否与上述对照表一致
- 🎯 **映射逻辑优化**: 优先通过描述匹配，确保字段重命名后功能正常
- 🛡️ **智能字段管理**: 避免重复创建，自动补充缺失描述
#### 🔧 **用户体验优化**
- 💡 **数据库配置提示增强**: 优化数据库未配置时的提示体验
  - 使用持久提示替代自动消失提示，需要用户主动关闭
#### 🐛 **缺陷修复**
- ⏱️ **时长显示修复**: 修复本地媒体时长显示问题，现在能正确获取并显示本地视频文件的时长信息
- 🔧 **兼容性提升**: 修复crypto.randomUUID兼容性问题，提升在不同环境下的稳定性
- 🔄 **标签刷新修复**: 修复清空标签（增删媒体）后刷新功能失效的问题，确保根据标签描述信息正确执行智能添加

### 📅 v0.4.3版本更新 (2025.7.17)
- 🆕 新增功能
  - 🏷️ 媒体块自定义属性系统: 为插件生成的所有媒体相关块添加标准化自定义属性，实现智能识别和高效管理
    - 📊 **统一标识体系**: 所有媒体功能生成的块都将自动添加特定的自定义属性，便于后续识别、查询和管理，为构建个人媒体学习系统和内容管理工作流提供强大基础
    - ⏰ **时间戳块增强**: 生成的时间戳链接块自动添加 `custom-media="timestamp"` 属性标识，并通过 `custom-timestamp="02:03"` 精确记录时间点，支持批量管理和快速定位重要时刻
    - 🔄 **循环片段智能标记**: 循环片段块使用 `custom-media="loop"` 进行标识，同时通过 `custom-loop-start="01:30"` 和 `custom-loop-end="02:45"` 精确记录循环区间，支持复杂的片段管理和学习重点标记
    - 📸 **截图块自动识别**: 截图功能生成的图片块添加 `custom-media="screenshot"` 属性，实现截图内容的快速筛选、分类整理和视觉化管理
    - 📷 **截图+时间戳组合卡片**: 截图带时间戳功能生成的复合块使用 `custom-media="mediacard"` 标识，配合 `custom-timestamp="02:03"` 关联时间信息，形成完整的媒体记忆卡片体系
    - 📔 **媒体笔记完整档案**: 媒体笔记文档添加 `custom-type="MediaNote"` 类型标识，通过 `custom-mediaurl="媒体URL"` 记录源媒体链接，使用 `custom-website="bilibili/openlist/webdav/local"` 标记来源网站，构建完整的学习档案系统
    - 🔍 **SQL查询支持**: 基于自定义属性实现精确的媒体块检索，支持复杂的数据分析和内容管理需求
      ```sql
      -- 查询所有时间戳块，快速定位媒体标记点
      SELECT * FROM blocks WHERE id IN (
          SELECT block_id FROM attributes WHERE name = 'custom-media' AND value = 'timestamp'
      );

      -- 查询所有媒体笔记，构建媒体学习档案
      SELECT * FROM blocks WHERE id IN (
          SELECT block_id FROM attributes WHERE name = 'custom-type' AND value = 'MediaNote'
      );

      -- 查询特定来源的媒体笔记，如B站学习内容统计
      SELECT * FROM blocks WHERE id IN (
          SELECT block_id FROM attributes WHERE name = 'custom-website' AND value = 'bilibili'
      );
      ```
    - 🎯 **应用场景扩展**: 支持构建个人媒体学习系统、内容管理工作流、媒体资源分析、学习进度追踪等高级应用，为用户提供更强大的媒体内容组织和利用能力

- 🐛 缺陷修复
  - 🎯 dock栏图标修复: 修复dock栏图标调整位置导致空白问题，确保图标正常显示
- 🔧 开发重构
  - 📋 面板命名统一: 统一各功能面板命名规范，提升代码一致性
  - 🎨 样式布局优化: 统一样式布局设计，改善用户界面体验

### 📅 v0.4.2版本更新 (2025.7.15)
- ✨ 功能优化
  - 🎯 媒体笔记设置优化: 重构目标笔记本/文档设置项逻辑，简化配置流程，提升用户体验
  - 📝 智能笔记面板集成: 创建媒体笔记时自动添加到笔记面板，实现一键创建、自动管理的无缝体验
    - 🔄 自动同步：媒体笔记创建后立即出现在笔记面板中，无需手动添加

### 📅 v0.4.1版本更新 (2025.7.14)
- 🆕 新增功能
  - 🔗 数据库URL链接识别: 支持数据库中URL字段的媒体链接点击直接打开播放器，无需跳转浏览器
  - ⌨️ Ctrl+点击强制浏览器: 按住Ctrl键点击媒体链接可强制使用浏览器打开，绕过插件处理
  - 📝 笔记面板功能: 新增独立的笔记面板，提供便捷的文档和块内容编辑功能
    - 📋 ID输入支持：支持输入文档ID或块ID，自动验证格式并获取内容
    - 🏷️ 智能标签：添加时自动截取前4个字符作为标签名称
    - 🖱️ 右键菜单：提供完整的标签管理操作
      - ✏️ 重命名：点击重命名可编辑标签名称，支持键盘确认和失焦保存
      - 🔗 在思源中打开：直接在思源中打开对应的文档或块，快速跳转
      - 📋 复制ID：一键复制文档或块的ID到剪贴板
      - 🗑️ 删除标签：移除不需要的笔记标签，自动清理配置
    - 📄 完整渲染：使用Protyle组件完整渲染文档内容，支持滚动和交互
    - 💾 状态保存：自动保存标签页配置，重启后恢复之前的笔记标签
  - 📔 媒体笔记增强: 创建媒体笔记支持在指定文档下创建子文档，提供更灵活的笔记组织方式
    - 🔍 智能搜索配置：设置中支持输入文档名称或笔记本名称进行搜索，快速定位目标位置
    - 📂 子文档创建：支持在选定的父文档下自动创建子文档，保持笔记结构的层次性和组织性

### 📅 v0.4.0版本更新 (2025.7.12)
- 🆕 新增功能
  - 📚 B站合集支持: 新增B站合集批量添加功能，通过任意合集视频链接一键获取整个合集
    - 🏷️ 智能识别：自动检测视频是否属于合集，提取合集ID和UP主信息
    - 🔄 智能刷新：支持合集标签的一键刷新更新
- 🔧 性能优化
  - 💬 提示信息精简: 优化批量添加提示，统一showMessage处理逻辑
- 🐛 缺陷修复
  - 🔊 B站视频音频修复: 修复B站视频播放时只有画面无声音的问题，统一DASH流处理逻辑
  - ⚡ 播放参数统一: 重构B站视频播放流程，直接生成blob播放地址，简化代码逻辑
  - 🔇 警告信息优化: 消除播放B站/WebDAV视频时的无关OpenList错误警告，提升用户体验

### 📅 v0.3.9版本更新 (2025.7.12)
- 🆕 新增功能
  - 📝 标签描述存储: 批量添加文件夹或B站收藏夹时，自动将路径/ID信息保存到所在标签选项描述中
    - 📁 文件夹标签：保存完整文件夹路径，如 `C:\Users\<USER>\MyPlaylist`
    - 💖 B站收藏夹：保存收藏夹ID，如 `12345678`
  - 🔄 标签智能刷新: 基于标签描述信息，右键菜单新增智能刷新功能
    - 🧠 智能识别：自动识别文件夹路径或B站收藏夹ID，选择对应刷新策略
    - 📁 文件夹同步：检测本地文件夹变化，智能增删媒体项
    - 💖 收藏夹同步：检测B站收藏夹内容变化，保持数据一致性
    - ⚡ 差异化更新：智能对比现有数据，仅处理变化项目，性能提升90%以上
- 🐛 缺陷修复
  - 🔄 批量添加修复: 修复批量添加文件夹和B站收藏夹时删除其他项的竞态条件问题，确保数据安全
  - ⚡ 代码优化: 极限精简核心函数实现，代码减少40%，提升执行效率和可读性

### 📅 v0.3.8版本更新 (2025.7.11)
- 🆕 新增功能
  - ⌨️ 播放速度快捷键: 新增Ctrl+↑/↓快捷键控制播放速度，操作更便捷
  - ⚡ 倍速提升: 播放速度上限从2倍提升至5倍速，满足快速浏览需求
  - 🗂️ 数据库智能绑定: 支持数据库ID和数据库块ID双重输入方式，自动识别转换
  - 🔗 数据库绑定模式: 新增数据库绑定开关，启用时需要手动绑定数据库，禁用时自动生成本地数据库配置文件，提供灵活的配置方式
  - 💾 播放列表状态记忆: 自动保存视图模式和标签页状态，重启后无缝恢复
- 🐛 缺陷修复
  - 🎬 B站分P视频修复: 修复B站分P视频生成时间戳/循环片段链接缺少分P参数问题
  - 🎮 外部播放器修复: 修复PotPlayer和浏览器打开方式失效问题，外部播放器不再无意义打开tab
  - 🔗 OpenList链接识别: 修复OpenList时间戳/循环片段链接识别问题，支持三种URL格式
    - `/#/` 格式：`http://localhost:5244/#/mv/ve/001.mp4?t=2.4`
    - `/p/` 格式：`http://localhost:5244/p/mv/ve/001.mp4?t=2.4`
    - 直接路径：`http://localhost:5244/mv/ve/001.mp4?t=2.4`
  - ⚡ 设置即时生效: 修复设置播放器类型后链接点击不能及时生效问题
  - 📺 B站功能恢复: 修复B站字幕和视频总结无法获取问题
- 🔧 开发重构
  - 📊 播放列表API重构: 移除文件系统操作，全面采用思源笔记API，提升稳定性和性能
  - 🛠️ 数据库映射优化: 放宽字段映射条件，支持多字段或缺失字段的灵活处理，增强容错性
### 📅 v0.3.7版本更新 (2025.7.8)
- 🐛 缺陷修复
  - 📊 播放列表数据库优化: 修复数据库字段规范性问题，单选多选项具有正确颜色标识，自动创建画廊视图
  - 🔄 拖拽排序修复: 修复拖拽排序后播放列表无法加载的问题，确保多标签环境下数据完整性
- 🔧 开发重构
  - ⚡ 重写说明文档: 优化说明文档样式，增加数据库配置、账号配置等方法
### 📅 v0.3.6版本更新 (2025.7.5)
- 🆕 新增功能
  - ☁️ WebDAV云存储支持: 新增WebDAV云存储集成，包含设置面板配置、标签菜单浏览选项、直接流式播放、时间戳链接和循环片段支持
  - 🎛️ 顶部快捷菜单: 增加顶部紫色小电视图标，快速访问设置
  - 🎨 专用图标: 为OpenList和WebDAV服务添加专用SVG图标
- ✨ 功能改进
  - 🖼️ 图片本地化: 自动转换封面图和艺术家头像为本地资源，提升加载速度和离线显示支持
  - 📔 媒体笔记增强: 新创建的文档自动在右侧标签页打开
  - 🔄 循环功能增强: 增加单项循环和列表循环设置的互斥功能
  - 📝 术语优化: 将"循环次数"更新为"片段循环次数"，"循环后暂停"更新为"片段循环后暂停"
  - 📁 思源空间相对路径: 思源空间媒体现在使用相对路径生成时间戳和循环片段链接，与思源空间菜单项路径格式保持一致，提升可移植性和工作空间独立性
- 🐛 缺陷修复
  - 🔗 B站时间戳链接: 修复时间戳链接生成使用播放地址而非标准链接的问题
  - 📤 导出功能: 修复字幕、弹幕、AI总结导出功能问题
  - 📔 媒体笔记: 修复文档创建失败问题
  - 📸 截图功能: 修复截图不包含时间戳选项失效的问题
  - 🏷️ 播放列表标签菜单: 修复右键菜单重命名功能点击后菜单不隐藏的问题
  - 🔄 循环功能: 修复B站视频单项循环不工作、列表循环加载下一个媒体后暂停的问题，增强B站分P视频系列支持
  - 🎨 样式作用域: 修复SCSS选择器影响思源笔记滚动条的问题
- 🔧 技术改进
  - 统一核心模块代码结构，提升稳定性和性能
  - 实现静音自动播放绕过策略，确保播放列表循环可靠性
  - 统一所有媒体类型的循环机制
---
### 📅 v0.3.5版本更新 (2025.7.2)
⚠️ 重要提醒：由于大幅重构，本次更新导致播放列表和设置配置不兼容！
📋 请在更新前做好数据迁移备份，配置文件位于：data\storage\petal\siyuan-media-player\config.json
🔄 更新后需要重新配置播放列表和相关设置
- 📋 播放列表重构: 优化播放列表组件结构和性能，提升大量媒体时的响应速度
- ⚙️ 设置组件重构: 移除复杂样式，简化为直观的开关项界面，统一组件处理逻辑，大幅提升配置效率，增加数据库avid和笔记本ID的实时显示，移除保存和重置按钮，实现实时保存和单项重置
- 📚 数据库配置同步: 播放列表配置自动同步到数据库，确保数据一致性
- 🎯 拖拽功能增强: 支持拖拽媒体项进行排序和跨标签移动，支持拖拽播放列表标签重新排序，删除传统排序按钮，统一使用拖拽操作
- 📷 截图功能修复: 修复截图带时间戳功能，确保截图与时间戳正确关联
- 🏷️ 视觉标签优化: 增加播放列表项来源和类型的可视化标签，界面更直观美观
- 👨‍💼 账号样式优化: 改进B站账号显示样式，提升用户体验
- 💬 思源空间增强: 完整浏览思源笔记工作空间文件系统，支持浏览和播放所有文件夹中的媒体文件
- 🔗 媒体笔记URL修复: 修复B站媒体笔记中URL使用播放地址而非标准链接的问题
- 🧹 功能精简: 移除内置脚本加载功能，建议使用思源笔记内置JS脚本功能
- 🔄 网盘重构: 将AList云盘功能重构为OpenList，统一云存储接口，提升兼容性和稳定性
- ⚡ 代码优化: 极限精简核心代码，清理冗余逻辑，减少插件体积
- 🎯 专注核心: 专注于媒体播放和笔记集成功能，提升稳定性和性能
---
### 📅 v0.3.4版本更新 (2025.5.26)
- 🎛️ 按钮布局: 优化功能按钮位置，提升使用体验
- 🔄 图标更新: 修改dock栏图标，提高可识别性
- 🔍 链接逻辑: 重构媒体链接检查逻辑，支持本地媒体链接
- 🛠️ 问题修复: 修复本地媒体时间戳链接无法正常工作的问题
- ✨ 细节优化: 多项细节改进和功能优化
---
### 📅 v0.3.3版本更新 (2025.5.18)
- 🚀 界面大改: 移除顶部图标，通过dock栏按钮打开播放器
- 🎛️ 按钮布局: 将功能按钮移动到顶部，可以通过开关隐藏
- 🔂 循环增强: 增加单项循环和列表循环功能
- ⏸️ 暂停控制: 增加循环后暂停功能
- 💬 弹幕列表: 增加弹幕列表，可以便捷导出弹幕内容
- 👤 账号优化: 优化账号显示方式
- 🔄 排序功能: 新增播放列表排序功能，支持按默认、名称、时间和类型排序
- 📜 脚本加载: 支持加载自定义JavaScript脚本，可通过设置界面管理脚本状态（初步）
---
### 📅 v0.3.2版本更新 (2025.5.11)
- 🎨 界面统一: 优化UI，统一助手、播放列表、设置面板风格
- ☁️ 网盘支持: 增加AList网盘支持，扩展媒体来源
- 📂 文件选择: 支持本地文件直接选择文件导入
- 📋 菜单优化: 优化标签菜单，移除复杂的标签+右键逻辑
- ↔️ 面板调整: 支持面板拖拽放大缩小，灵活调整界面
- ⏸️ 循环设置: 增加循环播放后暂停设置选项
- 📝 插入方式: 扩展插入文档的方式，提供更多选择
- 🔗 链接增强: 扩展链接格式，支持一次插入时间戳和截图
- 📔 媒体笔记: 增加创建媒体笔记功能，提供自定义模版，支持设置快捷键，可选择笔记本创建或在当前文档中插入
- 🎛️ 格式统一: 统一自定义格式，增加恢复默认格式功能
- 🔄 打开方式: 支持自定义播放器标签页打开方式（新标签、右侧标签、底部标签、新窗口）
- 💻 开发增强: 完善开发者API，提供更丰富的接口和事件支持
- ✨ 更多优化: 众多细节优化，等待你的探索
---
### 📅 v0.3.1版本更新(2025.4.26)
- 💬 B站字幕控制：支持通过字幕按钮在播放器界面显示或隐藏B站视频字幕
- 📜 字幕自动滚动：媒体助手字幕列表现在会跟随播放进度自动滚动
- 🎨 界面优化：改进媒体助手UI界面，提升用户体验
- 🛠️ 问题修复：解决了文件路径中特殊字符导致的添加错误
- 📸 截图功能优化：改进截图功能，支持直接复制图片到剪贴板
- 🔄 字幕处理统一：统一字幕处理逻辑，提升播放器性能
---
### 📅 v0.3.0版本更新(2025.4.23)
- 🎯 B站弹幕支持：添加B站视频弹幕显示功能
- ✨ Pro功能引入：新增可选的Pro版功能
- 🔖 B站收藏夹增强：直接选择添加到播放列表
- 🧠 媒体助手功能：字幕浏览和视频摘要功能
- 💬 字幕支持：支持本地媒体和B站视频字幕
- 📑 视频摘要：AI生成视频内容概要（目前仅支持B站视频）
---
### 📅 v0.2.6 (2025.4.16)
1. 新增外部播放器选项：PotPlayer和浏览器，提供更灵活的媒体查看方式
2. 新增快捷浏览器打开功能：按住Ctrl键点击链接可直接通过浏览器打开
3. 播放列表中的媒体URL现在可点击，方便在浏览器中查看
4. 修复B站分P视频重复添加到播放列表的问题
5. 优化播放器界面元素，提升用户体验
---
### 📅 v0.2.5 (2025.4.10)
6. 新增B站收藏夹直接导入功能，可一键添加整个收藏夹视频到播放列表
7. 新增4种播放列表视图模式（详细视图、简洁视图、网格视图和封面视图），满足不同浏览习惯
8. 修复B站视频分p生成时间戳和循环片段链接问题，现在链接可正确跳转到对应分p
9. 重构播放列表系统，拆分为多个功能模块，提升性能和用户体验
10. 优化列表加载速度，提高大量视频时的响应性能
---
### 📅 v0.2.2 (2025.4.7)
- 修复问题
  - 修复某些网络视频解析失败的问题
  - 解决播放列表管理中的稳定性问题
  - 修复链接生成和解析中的边缘情况
- 性能优化
  - 优化内存使用，减少资源占用
  - 提高播放器加载速度
  - 改进缓存机制，减少重复加载
- UI改进
  - 细节优化，提升用户体验
  - 修复部分界面元素显示异常
  - 改进移动端适配效果
---
### 📅 v0.2.1 (2025.4.2)
- 新增B站分P列表功能
  - 自动罗列所有分P视频
  - 双击其他项时自动折叠展开的分P列表
  - 支持分P预览缩略图显示
- 增强播放器语言支持
  - 自动根据思源语言设置切换播放器界面语言
  - 完善英文界面翻译
  - 优化多语言字符显示
- 优化网络媒体加载
  - 提高Samba等网络视频的解析成功率
  - 延长超时时间并增加自动重试机制
  - 为网速较慢环境提供更好支持
  - 添加加载进度指示
---
### 📅 v0.2.0 (2025.3.28)
- 新增国际化(i18n)支持
  - 完整支持中英文界面本地化
  - 根据系统设置动态切换语言
  - 切换语言时自动翻译播放列表名称
  - 支持自定义翻译文本
- 改进用户界面
  - 优化播放控制栏布局
  - 添加更多视觉反馈
  - 改进移动端适配
- 提升兼容性
  - 修复在不同操作系统上的显示问题
  - 优化在低性能设备上的表现
---
### 📅 v0.1.9 (2024.3.27)
- 新增本地文件夹批量导入媒体功能
  - 支持递归扫描子文件夹
  - 自动过滤非媒体文件
  - 保留文件夹结构信息
- 新增播放列表清空功能
  - 支持单个列表清空
  - 提供确认机制防止误操作
- 修复播放列表标签切换问题
- 修复/命令菜单错位问题
- 优化整体稳定性
---
### 📅 v0.1.8 (2025.3.26)
- 增加B站DASH流支持
  - 解决音画不同步问题
  - 提升播放稳定性
  - 支持更高清晰度选项
- 优化链接处理
  - 修复时间戳和循环片段链接问题
  - 改进播放器标签页未打开时的链接点击行为
  - 增强链接解析容错能力
- 简化设置选项
  - 移除冗余设置项
  - 新增链接插入方式选择（光标位置或剪贴板）
  - 改进设置界面布局
- 修复已知问题并优化性能
  - 解决内存泄漏问题
  - 提高加载速度
  - 优化渲染性能
---
### 📅 v0.1.7 (2025.02.05)
- 改进内容插入机制
  - 将直接插入块改为复制到剪贴板
  - 用户可以自由选择粘贴位置
  - 添加更多插入反馈
- 修复/命令菜单错位的问题
- 性能优化与稳定性提升
  - 减少资源占用
  - 提高响应速度
- 非常抱歉，最近一段时间比较忙，全力备考，不能及时更新了，大家先将就用。
---
### 📅 v0.1.6 (2025.1.27)
- 优化B站视频分P支持
  - 正确获取分P视频的cid
  - 标题中显示分P信息
  - 支持通过p参数直接跳转到指定分P
  - 改进分P列表交互
- 优化循环播放功能
  - 循环次数可在设置中配置(1-10次)
  - 播放器界面显示循环进度
  - 统一配置管理
  - 添加循环播放视觉提示
- 修复已知问题
  - 修复链接处理逻辑（正确识别媒体链接）
  - 优化代码结构
  - 解决UI呈现不一致问题
---
### 📅 v0.1.5 (2025.1.26)
- 重构播放逻辑，提升稳定性
  - 改进媒体加载机制
  - 增强错误处理和恢复能力
- 增加循环片段支持
  - 可设置开始和结束时间点
  - 循环次数可配置
  - 循环区间可视化显示
- 修复已知问题
  - 解决播放中断问题
  - 修正时间戳生成错误
- 优化性能表现
  - 减少CPU和内存占用
  - 提高响应速度
---
### 📅 v0.1.1 (2025-01-23)
- 重构播放逻辑，提升稳定性
- 增加循环片段支持
- 修复已知问题
- 优化性能表现
- 改进用户界面交互
---
### 📅 v0.0.1 (2025-01-18)
- 初始版本发布
- 基础播放功能
  - 支持本地和网络视频播放
  - 播放控制（播放/暂停、音量、进度）
- B站视频支持
  - 解析B站链接
  - 自动提取视频信息
- 播放列表管理
  - 创建和编辑播放列表
  - 媒体项排序和删除
